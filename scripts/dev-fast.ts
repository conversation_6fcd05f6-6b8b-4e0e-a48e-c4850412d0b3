#!/usr/bin/env tsx
/**
 * 快速开发启动脚本
 * 优化开发环境启动速度
 */

import { spawn } from 'child_process'
import { existsSync, mkdirSync, writeFileSync } from 'fs'
import { join } from 'path'

const projectRoot = process.cwd()

// 创建临时缓存目录
const cacheDir = join(projectRoot, 'node_modules/.vite-cache')
if (!existsSync(cacheDir)) {
  mkdirSync(cacheDir, { recursive: true })
}

// 创建预构建配置
const prebuildConfig = {
  optimizeDeps: {
    force: false, // 不强制重新构建
    include: [
      'vue',
      'vue-router',
      'pinia',
      'axios',
      '@vueuse/core',
      'vue-i18n',
      'js-cookie',
      'element-plus/es/components/form/style/css',
      'element-plus/es/components/form-item/style/css',
      'element-plus/es/components/button/style/css',
      'element-plus/es/components/input/style/css',
      'element-plus/es/components/checkbox/style/css',
      'element-plus/es/components/loading/style/css',
      'element-plus/es/components/message/style/css'
    ]
  }
}

console.log('🚀 启动快速开发模式...')
console.log('📦 优化预构建配置...')

// 设置环境变量
process.env.VITE_OPTIMIZE_DEPS = 'true'
process.env.NODE_ENV = 'development'

// 启动 Vite 开发服务器
const viteProcess = spawn('vite', ['--open', '--host'], {
  stdio: 'inherit',
  shell: true,
  cwd: projectRoot
})

viteProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error)
  process.exit(1)
})

viteProcess.on('close', (code) => {
  console.log(`🔚 开发服务器已关闭，退出码: ${code}`)
  process.exit(code || 0)
})

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭开发服务器...')
  viteProcess.kill('SIGINT')
})

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭开发服务器...')
  viteProcess.kill('SIGTERM')
})
