// 登录页面专用样式 - 轻量级版本
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .login-form {
    width: 400px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    
    .title {
      text-align: center;
      color: #333;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 30px;
    }
    
    .login-code {
      width: 33%;
      height: 40px;
      float: right;
      
      .login-code-img {
        cursor: pointer;
        vertical-align: middle;
        height: 40px;
        border-radius: 4px;
      }
    }
    
    .oauth-login-item {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 8px;
      background: #f5f5f5;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #e0e0e0;
        transform: translateY(-2px);
      }
    }
    
    .code-bgc {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
    }
  }
  
  .copyright {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    
    a {
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Element Plus 组件样式覆盖
.el-form-item {
  margin-bottom: 20px;
}

.el-input {
  .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }
}

.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  }
}

.el-checkbox {
  .el-checkbox__label {
    color: #666;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login {
    padding: 20px;
    
    .login-form {
      width: 100%;
      max-width: 400px;
      padding: 30px 20px;
    }
  }
}

// 加载动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
